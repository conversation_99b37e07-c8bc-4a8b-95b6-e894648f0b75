<template>
  <!-- Preload critical CSS to prevent FOUC -->
  <link rel="preload" as="style" href="/css/dialog-styles.css" />

  <Teleport to="body">
    <Transition
      name="modal"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        @click.self="cancel"
        role="dialog"
        aria-modal="true"
        aria-labelledby="dialog-title"
      >
        <!-- CSS Containment for layout stability -->
        <div
          class="export-invoice-dialog bg-secondary rounded-lg shadow-2xl p-1 max-w-6xl w-full mx-4 max-h-[95vh] flex flex-col overflow-hidden"
          style="contain: layout style paint"
        >
          <!-- Header with proper semantic structure -->
          <header
            class="flex justify-between items-center p-2 border-b border-gray-200"
          >
            <div class="w-6"></div>
            <!-- Spacer for centering -->
            <h1 id="dialog-title" class="text-lg font-bold text-center">
              Xuất hóa đơn
            </h1>
            <button
              @click="cancel"
              class="text-red-500 hover:text-red-700 transition-colors duration-200 p-1 rounded-full hover:bg-red-50"
              aria-label="Đóng dialog"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="w-5 h-5"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
            </button>
          </header>
          <!-- Main content with optimized layout -->
          <main class="flex-1 p-2 overflow-hidden" style="contain: layout">
            <div
              v-if="!isInvoicePublished"
              class="grid grid-cols-1 lg:grid-cols-12 gap-4 h-full"
            >
              <!-- Left Column - Form Section -->
              <section
                class="lg:col-span-5 space-y-4 overflow-y-auto scroll-smooth"
                style="contain: layout style; max-height: calc(100vh - 200px)"
              >
                <!-- Invoice Information Card -->
                <div
                  class="invoice-info-card p-4 rounded-lg bg-white shadow-sm border"
                >
                  <h2 class="text-primary font-bold text-base mb-3">
                    Thông tin xuất hóa đơn
                  </h2>

                  <!-- Async load TabChangeOrder with fallback -->
                  <Suspense>
                    <template #default>
                      <TabChangeOrder
                        :tabs="tabs"
                        @toogleTab="handleSetTab"
                        class="mb-4"
                      />
                    </template>
                    <template #fallback>
                      <div
                        class="h-8 bg-gray-200 rounded animate-pulse mb-4"
                      ></div>
                    </template>
                  </Suspense>

                  <!-- Optimized form with better accessibility and performance -->
                  <form
                    class="grid grid-cols-2 gap-x-4 gap-y-4 text-sm"
                    @submit.prevent
                  >
                    <!-- Quick Select Dropdown -->
                    <div
                      v-if="dataVatCustomer?.length"
                      class="col-span-2 flex items-center gap-3"
                    >
                      <label
                        for="tax-code-select"
                        class="w-40 font-medium text-gray-700"
                      >
                        Chọn nhanh
                      </label>
                      <select
                        id="tax-code-select"
                        v-model="selectTaxCode"
                        @change="handleChangeTaxCode"
                        class="flex-1 outline-none bg-white px-3 py-2 rounded-md border border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                      >
                        <option disabled value="">Chọn nhanh mã số thuế</option>
                        <option
                          v-for="tax in dataVatCustomer"
                          :key="tax?.id"
                          :value="tax"
                        >
                          {{ tax?.company }}
                        </option>
                      </select>
                    </div>

                    <!-- Buyer Name -->
                    <div class="col-span-2 flex items-center gap-3">
                      <label
                        for="buyer-name"
                        class="w-40 font-medium text-gray-700"
                      >
                        Người mua hàng:
                      </label>
                      <input
                        id="buyer-name"
                        v-model="buyerName"
                        type="text"
                        placeholder="Nhập họ tên người mua"
                        class="flex-1 border-b-2 border-dashed border-gray-300 px-2 py-1 outline-none focus:border-primary transition-colors duration-200"
                        required
                      />
                    </div>

                    <!-- Tax Code (Company only) -->
                    <Transition
                      name="slide-fade"
                      enter-active-class="transition-all duration-300 ease-out"
                      enter-from-class="opacity-0 transform -translate-y-2"
                      enter-to-class="opacity-100 transform translate-y-0"
                      leave-active-class="transition-all duration-200 ease-in"
                      leave-from-class="opacity-100 transform translate-y-0"
                      leave-to-class="opacity-0 transform -translate-y-2"
                    >
                      <div
                        v-if="selected === 'COMPANY'"
                        class="col-span-2 flex items-center gap-3"
                      >
                        <label
                          for="tax-code"
                          class="w-40 font-medium text-gray-700"
                        >
                          Mã số thuế:
                        </label>
                        <input
                          id="tax-code"
                          v-model="buyerTaxCode"
                          type="text"
                          placeholder="Nhập mã số thuế"
                          class="flex-1 border-b-2 border-dashed border-gray-300 px-2 py-1 outline-none focus:border-primary transition-colors duration-200"
                          required
                        />
                      </div>
                    </Transition>

                    <!-- Company Name (Company only) -->
                    <Transition
                      name="slide-fade"
                      enter-active-class="transition-all duration-300 ease-out"
                      enter-from-class="opacity-0 transform -translate-y-2"
                      enter-to-class="opacity-100 transform translate-y-0"
                      leave-active-class="transition-all duration-200 ease-in"
                      leave-from-class="opacity-100 transform translate-y-0"
                      leave-to-class="opacity-0 transform -translate-y-2"
                    >
                      <div
                        v-if="selected === 'COMPANY'"
                        class="col-span-2 flex items-center gap-3"
                      >
                        <label
                          for="company-name"
                          class="w-40 font-medium text-gray-700"
                        >
                          Tên đơn vị:
                        </label>
                        <input
                          id="company-name"
                          v-model="buyerCompany"
                          type="text"
                          placeholder="Nhập tên đơn vị"
                          class="flex-1 border-b-2 border-dashed border-gray-300 px-2 py-1 outline-none focus:border-primary transition-colors duration-200"
                          required
                        />
                      </div>
                    </Transition>

                    <!-- Address -->
                    <div class="col-span-2 flex items-center gap-3">
                      <label
                        for="address"
                        class="w-40 font-medium text-gray-700"
                      >
                        Địa chỉ:
                      </label>
                      <input
                        id="address"
                        v-model="buyerAddress"
                        type="text"
                        placeholder="Nhập địa chỉ"
                        class="flex-1 border-b-2 border-dashed border-gray-300 px-2 py-1 outline-none focus:border-primary transition-colors duration-200"
                      />
                    </div>
                    <!-- Email Toggle -->
                    <div class="col-span-2 flex items-center gap-3 py-2">
                      <span class="w-40 font-medium text-gray-700"
                        >Gửi mail:</span
                      >
                      <label
                        class="relative inline-flex items-center cursor-pointer"
                      >
                        <input
                          v-model="isSendMail"
                          type="checkbox"
                          class="sr-only peer"
                        />
                        <div
                          class="w-11 h-6 bg-gray-300 rounded-full peer peer-checked:bg-primary transition-colors duration-300 ease-in-out"
                        ></div>
                        <div
                          class="absolute left-0.5 top-0.5 bg-white w-5 h-5 rounded-full transition-transform duration-300 ease-in-out peer-checked:translate-x-full shadow-sm"
                        ></div>
                      </label>
                    </div>

                    <!-- Email Fields (with smooth transitions) -->
                    <Transition
                      name="slide-fade"
                      enter-active-class="transition-all duration-300 ease-out"
                      enter-from-class="opacity-0 transform -translate-y-2"
                      enter-to-class="opacity-100 transform translate-y-0"
                      leave-active-class="transition-all duration-200 ease-in"
                      leave-from-class="opacity-100 transform translate-y-0"
                      leave-to-class="opacity-0 transform -translate-y-2"
                    >
                      <div v-if="isSendMail" class="col-span-2 space-y-4">
                        <!-- Receiver Name -->
                        <div class="flex items-center gap-3">
                          <label
                            for="receiver-name"
                            class="w-40 font-medium text-gray-700"
                          >
                            Tên người nhận:
                          </label>
                          <input
                            id="receiver-name"
                            v-model="receiverName"
                            type="text"
                            placeholder="Nhập tên người nhận"
                            class="flex-1 border-b-2 border-dashed border-gray-300 px-2 py-1 outline-none focus:border-primary transition-colors duration-200"
                            required
                          />
                        </div>

                        <!-- Receiver Email -->
                        <div class="flex items-center gap-3">
                          <label
                            for="receiver-email"
                            class="w-40 font-medium text-gray-700"
                          >
                            Email người nhận:
                          </label>
                          <input
                            id="receiver-email"
                            v-model="receiverEmail"
                            type="email"
                            placeholder="<EMAIL>"
                            class="flex-1 border-b-2 border-dashed border-gray-300 px-2 py-1 outline-none focus:border-primary transition-colors duration-200"
                            required
                          />
                        </div>
                      </div>
                    </Transition>
                  </form>
                  <!-- Action Buttons -->
                  <div
                    class="flex items-center justify-end gap-3 mt-6 pt-4 border-t border-gray-100"
                  >
                    <button
                      v-if="!isInvoicePublished"
                      @click="handleUpdateNewProductInvoice"
                      :disabled="isLoading"
                      class="px-4 py-2 bg-white text-primary border border-primary rounded-md hover:bg-primary hover:text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span class="flex items-center gap-2">
                        <svg
                          v-if="isLoading"
                          class="animate-spin w-4 h-4"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                          ></circle>
                          <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Cập nhật SP
                      </span>
                    </button>
                    <button
                      v-if="!isInvoicePublished"
                      @click="handleExportInvoiceDraft"
                      :disabled="isLoading"
                      class="px-4 py-2 bg-primary text-white border border-primary rounded-md hover:bg-primary-dark transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span class="flex items-center gap-2">
                        <svg
                          v-if="isLoading"
                          class="animate-spin w-4 h-4"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                          ></circle>
                          <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Cập nhật thông tin
                      </span>
                    </button>
                  </div>
                </div>
                <!-- Products Section -->
                <div
                  class="products-card p-4 bg-white rounded-lg shadow-sm border"
                >
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-primary font-bold text-base">Sản phẩm</h3>
                    <div class="flex items-center gap-2">
                      <!-- Main edit button -->
                      <div class="relative">
                        <button
                          @click.stop="toogleOpenEditPopup"
                          @mousedown.stop
                          @touchstart.stop="toogleOpenEditPopup"
                          class="p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-full transition-all duration-200 cursor-pointer relative z-20"
                          style="pointer-events: auto"
                          aria-label="Chỉnh sửa sản phẩm"
                          type="button"
                          title="Chỉnh sửa sản phẩm"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="w-5 h-5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Products List with virtual scrolling for performance -->
                  <div
                    v-if="dataItemInvoice?.length"
                    class="max-h-[28vh] overflow-y-auto scroll-smooth"
                    style="contain: layout style"
                  >
                    <Suspense>
                      <template #default>
                        <div>
                          <div
                            v-for="(product, index) in dataItemInvoice"
                            :key="product?.id || index"
                            class="border border-gray-200 px-3 py-2 my-2 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200"
                          >
                            <ItemProductLineInvoice :product="product" />
                          </div>
                        </div>
                      </template>
                      <template #fallback>
                        <div class="space-y-2">
                          <div
                            v-for="i in 3"
                            :key="i"
                            class="h-16 bg-gray-200 rounded animate-pulse"
                          ></div>
                        </div>
                      </template>
                    </Suspense>
                  </div>

                  <!-- Empty state -->
                  <div v-else class="text-center py-8 text-gray-500">
                    <svg
                      class="w-12 h-12 mx-auto mb-2 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"
                      ></path>
                    </svg>
                    <p>Chưa có sản phẩm nào</p>
                  </div>
                </div>
              </section>

              <!-- Right Column - Invoice Preview -->
              <section
                class="lg:col-span-7 bg-white rounded-lg shadow-sm border p-4 overflow-hidden"
                style="
                  contain: layout style;
                  height: calc(100vh - 200px);
                  min-height: 400px;
                "
              >
                <div class="flex items-center gap-2 mb-4">
                  <h2 class="text-primary font-bold text-base">
                    {{
                      isInvoicePublished ? "Hóa đơn chính thức" : "Hóa đơn nháp"
                    }}
                  </h2>
                </div>

                <!-- Invoice Preview with loading state -->
                <div class="h-full overflow-hidden rounded-lg">
                  <Suspense>
                    <template #default>
                      <!-- Try iframe first, fallback to external link if blocked -->
                      <div
                        v-if="dataInvoiceDraft && urlInvoice"
                        class="h-full relative"
                      >
                        <iframe
                          v-show="!iframeBlocked"
                          ref="invoiceIframe"
                          :src="urlInvoice"
                          width="100%"
                          height="100%"
                          class="rounded-lg border-0"
                          allowfullscreen
                          loading="lazy"
                          @error="handleIframeError"
                          @load="
                            () => {
                              iframeBlocked = false;
                            }
                          "
                        ></iframe>

                        <!-- Fallback when iframe is blocked -->
                        <div
                          v-if="iframeBlocked"
                          class="h-full flex items-center justify-center bg-gray-50 rounded-lg"
                        >
                          <div class="text-center p-6">
                            <svg
                              class="w-16 h-16 mx-auto mb-4 text-yellow-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"
                              ></path>
                            </svg>
                            <h3
                              class="text-lg font-semibold text-gray-800 mb-2"
                            >
                              Iframe bị chặn
                            </h3>
                            <p class="text-gray-600 mb-4">
                              Trình duyệt đã chặn hiển thị iframe. Vui lòng mở
                              hóa đơn trong tab mới.
                            </p>
                            <div class="space-y-2">
                              <button
                                @click="openInNewTab"
                                class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors duration-200"
                              >
                                Mở trong tab mới
                              </button>
                              <button
                                @click="retryIframe"
                                class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200"
                              >
                                Thử lại
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template #fallback>
                      <div
                        class="h-full flex items-center justify-center bg-gray-50 rounded-lg"
                      >
                        <div class="text-center">
                          <div
                            class="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-2"
                          ></div>
                          <p class="text-gray-600">Đang tải hóa đơn...</p>
                        </div>
                      </div>
                    </template>
                  </Suspense>

                  <!-- Empty state for no invoice -->
                  <div
                    v-if="!urlInvoice"
                    class="h-full flex items-center justify-center bg-gray-50 rounded-lg"
                  >
                    <div class="text-center">
                      <svg
                        class="w-16 h-16 mx-auto mb-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        ></path>
                      </svg>
                      <p class="text-gray-600 mb-2">Chưa có hóa đơn</p>
                      <p class="text-sm text-gray-500">
                        Vui lòng tạo hóa đơn nháp trước
                      </p>
                    </div>
                  </div>
                </div>
              </section>
            </div>
            <!-- Published Invoice View -->
            <div v-else class="grid grid-cols-12 h-full">
              <section
                class="col-span-12 bg-white p-4 rounded-lg shadow-sm border overflow-hidden"
                style="height: calc(100vh - 200px)"
              >
                <!-- Header with invoice number and copy button -->
                <div
                  class="flex items-center justify-between mb-4 pb-4 border-b border-gray-200"
                >
                  <div class="flex items-center gap-2">
                    <h2 class="text-primary font-bold text-lg">
                      Hóa đơn chính thức
                    </h2>
                    <span
                      v-if="numberOfinvoice?.value"
                      class="font-bold text-gray-700"
                    >
                      - {{ numberOfinvoice.value }}
                    </span>
                  </div>

                  <!-- Copy Link Button -->
                  <button
                    v-if="urlInvoice"
                    @click="handleCopy"
                    class="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="w-4 h-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184"
                      />
                    </svg>
                    <span class="hidden md:inline">Copy link hóa đơn</span>
                  </button>
                </div>

                <!-- Invoice Content -->
                <div class="h-full overflow-hidden rounded-lg">
                  <div v-if="urlInvoice" class="h-full relative">
                    <iframe
                      v-show="!iframeBlocked"
                      ref="invoiceIframe"
                      :src="urlInvoice"
                      width="100%"
                      height="100%"
                      class="rounded-lg border-0"
                      allowfullscreen
                      loading="lazy"
                      @error="handleIframeError"
                      @load="
                        () => {
                          iframeBlocked = false;
                        }
                      "
                    ></iframe>

                    <!-- Fallback when iframe is blocked -->
                    <div
                      v-if="iframeBlocked"
                      class="h-full flex items-center justify-center bg-gray-50 rounded-lg"
                    >
                      <div class="text-center p-6">
                        <svg
                          class="w-16 h-16 mx-auto mb-4 text-yellow-500"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"
                          ></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">
                          Iframe bị chặn
                        </h3>
                        <p class="text-gray-600 mb-4">
                          Trình duyệt đã chặn hiển thị iframe. Vui lòng mở hóa
                          đơn trong tab mới.
                        </p>
                        <div class="space-y-2">
                          <button
                            @click="openInNewTab"
                            class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors duration-200"
                          >
                            Mở trong tab mới
                          </button>
                          <button
                            @click="retryIframe"
                            class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200"
                          >
                            Thử lại
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Reload button for failed loads -->
                  <div
                    v-else
                    class="flex items-center justify-center h-full bg-gray-50 rounded-lg"
                  >
                    <div class="text-center">
                      <svg
                        class="w-16 h-16 mx-auto mb-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        ></path>
                      </svg>
                      <p class="text-gray-600 mb-4">Không thể tải hóa đơn</p>
                      <button
                        @click="handleGetInvoice"
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors duration-200"
                      >
                        Tải lại hóa đơn
                      </button>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          </main>

          <!-- Footer Actions - Fixed at bottom -->
          <footer
            class="flex-shrink-0 flex flex-col sm:flex-row justify-between sm:justify-end items-center gap-3 p-2 border-t border-gray-200 bg-gray-50"
          >
            <!-- Mobile: Show button info -->
            <div class="text-sm text-gray-600 sm:hidden">
              <span v-if="!isInvoicePublished"
                >Sẵn sàng xuất hóa đơn chính thức</span
              >
              <span v-else>Hóa đơn đã được xuất</span>
            </div>

            <!-- Export Button -->
            <button
              v-if="!isInvoicePublished"
              @click="toogleConfirmExportExportWareHouse"
              :disabled="isLoading"
              class="w-full sm:w-auto px-6 py-3 sm:py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-primary/50 font-medium"
            >
              <span class="flex items-center justify-center gap-2">
                <svg
                  v-if="isLoading"
                  class="animate-spin w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span class="sm:inline">Xuất hóa đơn chính thức</span>
                <span class="sm:hidden">Xuất hóa đơn</span>
              </span>
            </button>

            <!-- Published state info -->
            <div v-else class="text-sm text-green-600 font-medium">
              ✓ Hóa đơn đã được xuất thành công
            </div>
          </footer>

          <!-- Async Dialog Components -->
          <Suspense>
            <template #default>
              <div>
                <ExportInvoiceDialog
                  v-if="isConfirmExportInvoice"
                  :title="'Xác nhận xuất hóa đơn chính thức'"
                  :message="'Bạn có muốn xuất hóa đơn chính thức không. Vui lòng kiểm tra kĩ trước khi đồng ý.'"
                  @confirm="handleExportInvoice"
                  @cancel="toogleConfirmExportExportWareHouse"
                />

                <WaringWareHouse
                  v-if="isAlertWareHouse"
                  :title="'Không thể xuất hóa đơn'"
                  @cancel="isAlertWareHouse = false"
                />

                <WaringWareHouse
                  v-if="isAlertRole"
                  :title="'Không thể xuất hóa đơn'"
                  :isAlertRole="true"
                  @cancel="isAlertRole = false"
                />

                <EditItemProductInvoice
                  v-if="isOpenEditItemInvoice"
                  :order="order"
                  :dataItemInvoice="dataItemInvoice"
                  :dataInvoiceDraft="dataInvoiceDraft"
                  @cancel="toogleOpenEditPopup"
                  @confirm="handGetNewInvoiceDraft"
                />
              </div>
            </template>
            <template #fallback>
              <!-- Fallback for async components -->
              <div></div>
            </template>
          </Suspense>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import type { VatInvoiceRequestDTO } from "../../types/Invoice";

const props = defineProps(["order"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const isAlertWareHouse = ref(false);

// Computed properties for better performance
const isInvoicePublished = computed(
  () =>
    props.order?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
);

// Iframe handling for blocked content
const iframeBlocked = ref(false);
const invoiceIframe = ref<HTMLIFrameElement>();

const handleIframeError = () => {
  console.warn("Iframe failed to load, possibly blocked by browser");
  iframeBlocked.value = true;
};

const openInNewTab = () => {
  if (urlInvoice.value) {
    window.open(urlInvoice.value, "_blank", "noopener,noreferrer");
  }
};

const retryIframe = () => {
  iframeBlocked.value = false;
  // Force iframe reload
  nextTick(() => {
    if (invoiceIframe.value && urlInvoice.value) {
      // Try different approaches to reload iframe
      invoiceIframe.value.src = "";
      setTimeout(() => {
        if (invoiceIframe.value) {
          invoiceIframe.value.src = urlInvoice.value;
        }
      }, 100);
    }
  });
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
//
const selected = ref("");
const isSendMail = ref(false);
const dataInvoiceDraft = ref();
//
const {
  requestUnpublishVatInvoice,
  requestPublishVatInvoice,
  getInvoicesOfOrder,
  viewPublishedInvoice,
  updateInvoiceItem,
  getInvoiceItemOfInvoie,
} = useInvoice();
const { getVatInfoByOwnerPartyId, createVatInfo } = useCustomer();
// Validation helper function
const validateForm = () => {
  if (buyerTaxCode.value && !buyerCompany.value) {
    useNuxtApp().$toast.warning("Vui lòng nhập tên đơn vị");
    return false;
  }

  if (isSendMail.value) {
    if (!receiverName.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên người nhận");
      return false;
    }
    if (!receiverEmail.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập email người nhận");
      return false;
    }
  }

  if (selected.value === "COMPANY" && !buyerTaxCode.value) {
    useNuxtApp().$toast.warning("Vui lòng nhập mã số thuế");
    return false;
  }

  return true;
};

const handleExportInvoiceDraft = async () => {
  if (!validateForm()) return;
  const dataVatRequest = {
    sourceId: dataInvoiceDraft.value?.invoiceId
      ? dataInvoiceDraft.value?.invoiceId
      : props.order?.id,
    sourceType: dataInvoiceDraft.value?.invoiceId ? "invoice" : "order",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
    buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  //
  if (
    props.order?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_DRAFT"
  ) {
    await handleGetInvoiceOfOrderDraft();
    return;
  }
  //
  await handleGetUrl(dataVatRequest);
  await orderStore.updateOrder(props.order?.id);
};
const handleUpdateNewProductInvoice = async () => {
  const dataRequest = {
    sourceId: props.order?.id,
    sourceType: "order",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
    buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  await handleGetUrl(dataRequest);
};
const auth = useCookie("auth") as any;
const { data } = await useFetch("/data/setting.json");

// Use tab-isolated context instead of cookies
const { orgId } = useTabContext();
const { getInventoryV2 } = useWarehouse();

// Optimized inventory check with better performance
const handleCheckInventory = async (): Promise<boolean> => {
  const dataSettingOrg = data.value as any;
  const result = dataSettingOrg?.find(
    (org: any) => org?.storeId === orgId.value
  );

  if (!result?.isExportInvoiceForProduct) {
    return true;
  }

  try {
    const inventoryData =
      props.order?.activeOrderItemProfiles?.map((item: any) => ({
        productId: item?.orderLineItem.variant?.product?.id,
        variantId:
          item?.orderLineItem?.variant?.id ===
          item.orderLineItem.variant?.product?.id
            ? ""
            : item.orderLineItem.variant?.id,
        sku: item.orderLineItem.variant?.sku,
      })) || [];

    if (!inventoryData.length) return true;

    const res = await getInventoryV2(
      props.order?.order?.customAttribute?.facilityId,
      inventoryData
    );

    return res?.every((item: any) => item?.orderAble >= 5) ?? true;
  } catch (error) {
    console.error("Inventory check failed:", error);
    return true; // Allow export if check fails
  }
};
const orderStore = useOrderStore();
const isAlertRole = ref(false);
const handleExportInvoice = async () => {
  //
  // check roles
  const dataSettingOrg = data.value as any;
  const arrRoles = dataSettingOrg?.find(
    (org: any) => org.storeId === orgId.value
  );
  if (arrRoles?.rolesExportInvoice?.length) {
    // const arrRoles = ["SALE_ADMIN", "SALE_MANAGER"];
    const isRoleSaleAdmin = auth.value?.user?.roles?.filter((role: any) =>
      arrRoles?.rolesExportInvoice?.includes(role)
    );
    if (!isRoleSaleAdmin?.length) {
      isAlertRole.value = true;
      toogleConfirmExportExportWareHouse();

      return;
    }
  }
  if (buyerTaxCode.value) {
    if (!buyerCompany.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên đơn vị");
      toogleConfirmExportExportWareHouse();

      return;
    }
  }
  if (isSendMail.value) {
    if (!receiverName.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên người nhận");
      toogleConfirmExportExportWareHouse();

      return;
    }
    if (!receiverEmail.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập email người nhận");
      toogleConfirmExportExportWareHouse();

      return;
    }
  }
  if (!dataInvoiceDraft.value.invoiceId) {
    useNuxtApp().$toast.warning("Vui lòng tạo hóa đơn nháp ");
    toogleConfirmExportExportWareHouse();

    return;
  }
  //
  const res = await handleCheckInventory();
  if (!res) {
    const dataSettingOrg = data.value as any;
    const arrRoles = dataSettingOrg?.find(
      (org: any) => org?.storeId === orgId.value
    );
    if (arrRoles?.rolesExportInvoice?.length) {
      const arrRolesDefault = ["SALE_ADMIN", "SALE_MANAGER"];
      const isRoleSaleAdmin = auth.value?.user?.roles?.filter((role: any) =>
        arrRolesDefault?.includes(role)
      );
      if (!isRoleSaleAdmin?.length) {
        isAlertWareHouse.value = true;
        toogleConfirmExportExportWareHouse();
        return;
      }
    }
  }
  const dataVatRequest = {
    sourceId: dataInvoiceDraft.value?.invoiceId,
    sourceType: "invoice",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: buyerTaxCode.value,
    buyerCompany: buyerCompany.value,
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  if (selected.value === "COMPANY") {
    if (!buyerTaxCode.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập mã số thuế");
      toogleConfirmExportExportWareHouse();
      return;
    }
    if (!selectTaxCode.value) {
      const response = await createVatInfo(
        buyerName.value,
        buyerTaxCode.value,
        receiverEmail.value,
        props.order?.order?.ownerPartyId,
        buyerAddress.value,
        auth.value?.user?.id
      );
    }
  }
  try {
    isLoading.value = true;
    const response = await requestPublishVatInvoice(
      dataVatRequest,
      auth.value?.user?.id
    );
    if (response.code === 1) {
      props.order.order.customAttribute.exportVatInvoiceStatus =
        "INVOICE_PUBLISHED";
      await handleGetInvoiceOfOrder();
      await orderStore.handleGetInvoiceOfOrder();
      toogleConfirmExportExportWareHouse();
    }
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};

const urlInvoice = ref();
const isLoading = ref(false);
const dataItemInvoice = ref<any>([]);

// Watch for URL changes to reset iframe state
watch(urlInvoice, () => {
  iframeBlocked.value = false;
});
const handleGetUrl = async (dataVatRequest: VatInvoiceRequestDTO) => {
  isLoading.value = true;
  try {
    const response = await requestUnpublishVatInvoice(
      dataVatRequest,
      auth.value?.user?.id
    );
    if (response?.previewLink) {
      dataInvoiceDraft.value = response;
      urlInvoice.value = response?.previewLink;
      const res = await getInvoiceItemOfInvoie(
        dataInvoiceDraft.value?.invoiceId
      );
      dataItemInvoice.value = res;
    }
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const dataVatCustomer = ref<any>([]);
const selectTaxCode = ref();
const handleGetVat = async (ownerPartyId: string) => {
  try {
    const response = await getVatInfoByOwnerPartyId(ownerPartyId);
    dataVatCustomer.value = response;
    if (dataVatCustomer.value.length) {
      selectTaxCode.value = dataVatCustomer.value[0];
      //
      buyerName.value = selectTaxCode.value?.company;
      buyerAddress.value = selectTaxCode.value?.address;
      if (selected.value === "COMPANY") {
        buyerCompany.value = selectTaxCode.value?.company;
        buyerTaxCode.value = selectTaxCode.value?.taxCode;
        receiverName.value = selectTaxCode.value?.company;
        receiverEmail.value = selectTaxCode.value?.invoiceReceiveEmail1;
      }
    }
    return response;
  } catch (error) {
    throw error;
  }
};
const dataListInvoice = ref<any>([]);
const selectedInvoice = ref();
const handleGetInvoiceOfOrder = async () => {
  try {
    const response = await getInvoicesOfOrder(props.order.id);
    dataListInvoice.value = response;
    if (dataListInvoice.value?.length >= 0) {
      handleSetInvoice(dataListInvoice.value[0]);
    }
    return response;
  } catch (error) {
    throw error;
  }
};
const handleGetInvoiceOfOrderDraft = async () => {
  try {
    const response = await getInvoicesOfOrder(props.order.id);
    const dataVatRequest = {
      sourceId: response?.length > 0 ? response[0]?.id : props.order.id,
      sourceType: response?.length > 0 ? "invoice" : "order",
      signType: "MTT",
      includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
      buyerName: buyerName.value,
      buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
      buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
      buyerAddress: buyerAddress.value,
      sendMail: isSendMail.value,
      receiverName: isSendMail.value ? receiverName.value : "",
      receiverEmail: isSendMail.value ? receiverEmail.value : "",
    };
    await handleGetUrl(dataVatRequest);
    return response;
  } catch (error) {
    throw error;
  }
};
const urlLinkOfficial = ref();

const handleGetInvoice = async () => {
  isLoading.value = true;
  try {
    const response = await viewPublishedInvoice(selectedInvoice.value);
    dataInvoiceDraft.value = response;
    urlInvoice.value = response?.previewLink;
    urlLinkOfficial.value = response?.lookupLink;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const numberOfinvoice = ref();
const handleSetInvoice = async (invoice: any) => {
  numberOfinvoice.value = invoice?.attributes?.find(
    (item: any) => item?.name === "INV_NO"
  );

  selectedInvoice.value = invoice?.id;
  isLoading.value = true;
  try {
    const response = await viewPublishedInvoice(selectedInvoice.value);
    urlInvoice.value = response?.previewLink;
    urlLinkOfficial.value = response?.lookupLink;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
onMounted(async () => {
  selected.value = tabs.value[0].value;

  const order = props.order?.order;
  const customAttr = order?.customAttribute;
  buyerName.value = order?.ownerName;

  const exportStatus = customAttr?.exportVatInvoiceStatus;

  if (exportStatus === "INVOICE_DRAFT") {
    await handleGetInvoiceOfOrderDraft();
    return;
  }

  if (exportStatus === "INVOICE_PUBLISHED") {
    await handleGetInvoiceOfOrder();
    return;
  }

  const includeBuyerTaxCode = selected.value === "COMPANY";

  const dataVatRequest = {
    sourceId: props.order?.id,
    sourceType: "order",
    signType: "MTT",
    includeBuyerTaxCode,

    buyerName: order?.ownerName,
    buyerTaxCode: "",
    buyerCompany: "",
    buyerAddress: "",
    sendMail: false,
    receiverName: "",
    receiverEmail: "",
  };

  await Promise.allSettled([
    handleGetVat(order?.ownerPartyId),
    handleGetUrl(dataVatRequest),
  ]);

  await orderStore.updateOrder(props.order?.id);
});

const buyerName = ref<string>("");
const buyerTaxCode = ref<string>("");
const buyerCompany = ref<string>("");
const buyerAddress = ref<string>("");
const receiverName = ref<string>("");
const receiverEmail = ref<string>("");
const tabs = ref([
  { label: "Khách lẻ", value: "CUSTOMER" },
  { label: "Công ty", value: "COMPANY" },
]);
const handleSetTab = (name: string) => {
  selected.value = name;
};
const handleChangeTaxCode = () => {
  buyerName.value = selectTaxCode.value?.company;
  buyerAddress.value = selectTaxCode.value?.address;
  buyerCompany.value = selectTaxCode.value?.company;
  buyerTaxCode.value = selectTaxCode.value?.taxCode;
  receiverName.value = selectTaxCode.value?.company;
  receiverEmail.value = selectTaxCode.value?.invoiceReceiveEmail1;
};
const isConfirmExportInvoice = ref(false);
const toogleConfirmExportExportWareHouse = () => {
  isConfirmExportInvoice.value = !isConfirmExportInvoice.value;
};
const handleCopy = () => {
  console.log("urlLinkOfficial", urlLinkOfficial.value);
  navigator.clipboard.writeText(urlLinkOfficial.value);
  useNuxtApp().$toast.success("Đã lưu vào bộ nhớ tạm");
};
const dataChangeProduct = ref<any>([]);
const handleUpdateInvoice = async (data: any) => {
  if (!dataInvoiceDraft.value.invoiceId) {
    useNuxtApp().$toast.warning("Vui lòng tạo hóa đơn nháp");
    return;
  }
  if (dataChangeProduct.value.length >= 0) {
    const index = dataChangeProduct.value.findIndex(
      (item: any) => item.id === data.id
    );
    if (index !== -1) {
      dataChangeProduct.value[index] = data;
    } else {
      dataChangeProduct.value.push(data);
    }
  } else {
    dataChangeProduct.value.push(data);
  }
  console.log("dataChangeProduct", dataChangeProduct.value);
  await updateInvoiceItem(
    dataInvoiceDraft.value.invoiceId,
    dataChangeProduct.value[0],
    auth.value?.user?.id
  );
};
//
const isOpenEditItemInvoice = ref(false);
const toogleOpenEditPopup = () => {
  console.log("🔧 toogleOpenEditPopup clicked!");
  console.log("Current isOpenEditItemInvoice:", isOpenEditItemInvoice.value);
  console.log("dataItemInvoice:", dataItemInvoice.value);
  console.log("dataInvoiceDraft:", dataInvoiceDraft.value);
  console.log("order:", props.order);

  // Check if we have required data
  if (!dataItemInvoice.value?.length) {
    console.warn("⚠️ No dataItemInvoice available");
    useNuxtApp().$toast.warning("Chưa có dữ liệu sản phẩm để chỉnh sửa");
    return;
  }

  if (!dataInvoiceDraft.value?.invoiceId) {
    console.warn("⚠️ No invoice draft available");
    useNuxtApp().$toast.warning("Vui lòng tạo hóa đơn nháp trước");
    return;
  }

  isOpenEditItemInvoice.value = !isOpenEditItemInvoice.value;

  console.log("New isOpenEditItemInvoice:", isOpenEditItemInvoice.value);
};
const handGetNewInvoiceDraft = async () => {
  if (buyerTaxCode.value) {
    if (!buyerCompany.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên đơn vị");
      return;
    }
  }
  if (isSendMail.value) {
    if (!receiverName.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập tên người nhận");
      return;
    }
    if (!receiverEmail.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập email người nhận");
      return;
    }
  }
  if (selected.value === "COMPANY") {
    if (!buyerTaxCode.value) {
      useNuxtApp().$toast.warning("Vui lòng nhập mã số thuế");
      return;
    }
  }
  const dataVatRequest = {
    sourceId: dataInvoiceDraft.value?.invoiceId,
    sourceType: "invoice",
    signType: "MTT",
    includeBuyerTaxCode: selected.value === "COMPANY" ? true : false,
    buyerName: buyerName.value,
    buyerTaxCode: selected.value === "COMPANY" ? buyerTaxCode.value : "",
    buyerCompany: selected.value === "COMPANY" ? buyerCompany.value : "",
    buyerAddress: buyerAddress.value,
    sendMail: isSendMail.value,
    receiverName: isSendMail.value ? receiverName.value : "",
    receiverEmail: isSendMail.value ? receiverEmail.value : "",
  };
  await handleGetUrl(dataVatRequest);

  toogleOpenEditPopup();
};
</script>

<style scoped>
/* CSS Containment for performance optimization */
.export-invoice-dialog {
  contain: layout style paint;
}

.invoice-info-card,
.products-card {
  contain: layout style;
}

/* Smooth transitions */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Modal transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Fade transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Optimized scrollbar */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-smooth::-webkit-scrollbar {
  width: 6px;
}

.scroll-smooth::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scroll-smooth::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scroll-smooth::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animation optimization */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Hover effects with GPU acceleration */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  will-change: transform, box-shadow;
}

.hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .export-invoice-dialog {
    max-height: 100vh;
    margin: 0;
    border-radius: 0;
  }

  .grid.grid-cols-1.lg\\:grid-cols-12 {
    grid-template-rows: auto 1fr;
    gap: 1rem;
  }

  .lg\\:col-span-5,
  .lg\\:col-span-7 {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .export-invoice-dialog {
    padding: 0.25rem;
    max-width: 100vw;
    max-height: 100vh;
  }

  main {
    padding: 1rem;
  }

  .grid.grid-cols-1.lg\\:grid-cols-12 {
    display: flex;
    flex-direction: column;
    height: auto;
  }

  .lg\\:col-span-5,
  .lg\\:col-span-7 {
    max-height: none !important;
    height: auto !important;
    min-height: 250px;
  }

  /* Ensure footer is always visible */
  footer {
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

@media (max-width: 640px) {
  .export-invoice-dialog {
    max-height: 100vh;
    overflow-y: auto;
  }

  /* Stack layout on very small screens */
  .lg\\:col-span-5 {
    order: 1;
  }

  .lg\\:col-span-7 {
    order: 2;
    min-height: 300px;
  }

  /* Compact form on mobile */
  .grid.grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .col-span-2 {
    grid-column: span 1;
  }

  .flex.items-center.gap-3 {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .w-40 {
    width: 100%;
    margin-bottom: 0.25rem;
  }
}
</style>
